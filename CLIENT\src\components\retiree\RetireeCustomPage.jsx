import React from "react";
import CustomCreateUpdateDialog from "../retiree/RetireeDialog";
import CustomMenu from "../../global/components/CustomMenu";
import CustomTable from "../retiree/RetireeCustomTable";
import DashboardHeader from "../../global/components/DashboardHeader";
import PropTypes from "prop-types";
import ErrorBoundary from "../../global/components/ErrorBoundary";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  IconButton,
  Tooltip
} from "@mui/material";
import {
  Assessment as AssessmentIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Elderly as RetireeIcon,
  Refresh as RefreshIcon
} from "@mui/icons-material";
import { useQuery } from "@tanstack/react-query";
import api from "../../config/api";

const CustomPage = ({
  dataListName,
  schema,
  title = "",
  description = "",
  searchable = true,
  hasEdit = true,
  hasDelete = true,
  hasAdd = true,
  hasClose = true,
  customAddElement = null,
  customEditElement = null,
  additionalMenuOptions = [],
  ROWS_PER_PAGE = 20,
}) => {
  const pageTitle = title || "Retirees Management";
  const pageDescription = description || `Manage ${dataListName}`;
  const apiPath = `/${dataListName}`;

  // Add stats query
  const { data: stats, isLoading: statsLoading, refetch } = useQuery({
    queryKey: [dataListName, "stats"],
    queryFn: async () => {
      const response = await api.get(`${apiPath}/stats`);
      return response.data;
    },
    staleTime: 60000,
  });

  return (
    <ErrorBoundary>
      <DashboardHeader
        title={pageTitle}
        description={pageDescription}
        searchable={searchable}
        childElement={
          hasAdd ? (
            customAddElement || (
              <CustomCreateUpdateDialog
                endpoint={apiPath}
                schema={schema}
                dataListName={dataListName}
              />
            )
          ) : null
        }
      />

      {/* Enhanced Action Bar with Stats - matching other pages style */}
     <Paper sx={{ mt: 3, mx: 3, p: 2, mb: 2, borderRadius: 2, boxShadow: '0 4px 16px rgba(0,0,0,0.1)' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" flexWrap="wrap" gap={2}>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="h6" sx={{ color: '#375e38', fontWeight: 'bold' }}>
              Retirees Management
            </Typography>

            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => refetch()}
                disabled={statsLoading}
                color="primary"
                sx={{
                  backgroundColor: 'rgba(55, 94, 56, 0.1)',
                  '&:hover': { backgroundColor: 'rgba(55, 94, 56, 0.2)' }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Summary Statistics - matching other pages style */}
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ backgroundColor: '#e8f5e9', borderLeft: '4px solid #4caf50' }}>
              <CardContent sx={{ py: 1 }}>
                <Box display="flex" alignItems="center" gap={1}>
                  <AssessmentIcon sx={{ color: '#4caf50' }} />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total Retirees</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {statsLoading ? '0' : stats?.totalRecords || 0}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ backgroundColor: '#e3f2fd', borderLeft: '4px solid #2196f3' }}>
              <CardContent sx={{ py: 1 }}>
                <Box display="flex" alignItems="center" gap={1}>
                  <MoneyIcon sx={{ color: '#2196f3' }} />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total Benefits</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      ₱{statsLoading ? '0' : (stats?.totalBenefits || 0).toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ backgroundColor: '#fff3e0', borderLeft: '4px solid #ff9800' }}>
              <CardContent sx={{ py: 1 }}>
                <Box display="flex" alignItems="center" gap={1}>
                  <RetireeIcon sx={{ color: '#ff9800' }} />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Compulsory</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {statsLoading ? '0' : stats?.compulsoryRetirements || 0}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ backgroundColor: '#fce4ec', borderLeft: '4px solid #e91e63' }}>
              <CardContent sx={{ py: 1 }}>
                <Box display="flex" alignItems="center" gap={1}>
                  <PeopleIcon sx={{ color: '#e91e63' }} />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Optional</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {statsLoading ? '0' : stats?.optionalRetirements || 0}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      <Box sx={{ mx: 3 }}>
        <CustomTable
          dataListName={dataListName}
          apiPath={apiPath}
          ROWS_PER_PAGE={ROWS_PER_PAGE}
          columns={Object.keys(schema)
            .filter((key) => schema[key].show === true || key === "action")
            .map((key) => {
              const fieldSchema = schema[key];
              const column = {
                field: key,
                label: fieldSchema.label,
                type: fieldSchema.type,
                searchable: fieldSchema.searchable || false,
              };

              if (fieldSchema.type === "action") {
                column.render = (row) => (
                  <CustomMenu
                    additionalMenuOptions={additionalMenuOptions}
                    customEditElement={customEditElement}
                    hasEdit={hasEdit}
                    hasDelete={hasDelete}
                    hasClose={hasClose}
                    row={row}
                    schema={schema}
                    endpoint={apiPath}
                    dataListName={dataListName}
                  />
                );
              }

              if (fieldSchema.customRender) {
                column.render = (row) => fieldSchema.customRender(row);
              }

              return column;
            })}
        />
      </Box>
    </ErrorBoundary>
  );
};

CustomPage.propTypes = {
  dataListName: PropTypes.string.isRequired,
  schema: PropTypes.objectOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      show: PropTypes.bool,
      searchable: PropTypes.bool,
      customRender: PropTypes.func,
      default: PropTypes.any,
    })
  ).isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  searchable: PropTypes.bool,
  hasEdit: PropTypes.bool,
  hasDelete: PropTypes.bool,
  hasAdd: PropTypes.bool,
  customAddElement: PropTypes.element,
  customEditElement: PropTypes.element,
  additionalMenuOptions: PropTypes.arrayOf(PropTypes.elementType),
};

export default CustomPage;
