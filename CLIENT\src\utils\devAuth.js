import axios from 'axios';
import CryptoJS from 'crypto-js';
import env from './env';

/**
 * Development authentication utility
 * This is for development purposes only
 */

// Function to get a development token from the server
export const getDevToken = async () => {
  try {
    const response = await axios.get(`${env('SERVER_URL')}/dev-token`);
    return response.data.token;
  } catch (error) {
    console.error('Failed to get development token:', error);
    throw error;
  }
};

// Function to store encrypted token
export const storeDevToken = (token) => {
  try {
    const secretKey = env('SECRET_KEY');
    if (!secretKey) {
      throw new Error('SECRET_KEY not found in environment variables');
    }
    
    const encryptedToken = CryptoJS.AES.encrypt(token, secretKey).toString();
    localStorage.setItem('token', encryptedToken);
    console.log('✅ Development token stored successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to store development token:', error);
    return false;
  }
};

// Function to setup development authentication
export const setupDevAuth = async () => {
  try {
    console.log('🔧 Setting up development authentication...');
    
    // Get token from server
    const token = await getDevToken();
    
    // Store encrypted token
    const stored = storeDevToken(token);
    
    if (stored) {
      console.log('✅ Development authentication setup complete');
      console.log('🔄 Please refresh the page to use the new token');
      return true;
    } else {
      throw new Error('Failed to store token');
    }
  } catch (error) {
    console.error('❌ Failed to setup development authentication:', error);
    return false;
  }
};

// Function to clear stored token
export const clearToken = () => {
  localStorage.removeItem('token');
  console.log('🗑️ Token cleared from localStorage');
};

// Function to check if token exists
export const hasToken = () => {
  return !!localStorage.getItem('token');
};

// Add to window for easy access in development
if (typeof window !== 'undefined') {
  window.devAuth = {
    setup: setupDevAuth,
    clear: clearToken,
    hasToken: hasToken,
    getToken: getDevToken,
    store: storeDevToken
  };
  
  console.log('🔧 Development auth utilities available at window.devAuth');
  console.log('   - window.devAuth.setup() - Setup development authentication');
  console.log('   - window.devAuth.clear() - Clear stored token');
  console.log('   - window.devAuth.hasToken() - Check if token exists');
}
