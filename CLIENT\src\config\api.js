import axios from "axios";
import env from "../utils/env";
import CryptoJS from "crypto-js"; // Import CryptoJS

// Function to get the stored token (decrypt it first)
const getToken = () => {
  const encryptedToken = localStorage.getItem("token");
  if (!encryptedToken) {
    console.log("🔐 No encrypted token found in localStorage");
    return null;
  }

  const secretKey = env("SECRET_KEY");
  if (!secretKey) {
    console.error("❌ SECRET_KEY not found in environment variables");
    return null;
  }

  try {
    const bytes = CryptoJS.AES.decrypt(encryptedToken, secretKey);
    const decryptedToken = bytes.toString(CryptoJS.enc.Utf8);

    if (!decryptedToken) {
      console.error("❌ Token decryption resulted in empty string");
      return null;
    }

    console.log("✅ Token successfully decrypted");
    return decryptedToken;
  } catch (error) {
    console.error("❌ Error decrypting token:", error);
    return null;
  }
};

const api = axios.create({
  baseURL: env("SERVER_URL"),
  withCredentials: true,
});

// Attach token to requests
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log(`🔑 Token attached to ${config.method?.toUpperCase()} ${config.url}`);
    } else {
      console.warn(`⚠️ No token available for ${config.method?.toUpperCase()} ${config.url}`);
    }
    return config;
  },
  (error) => {
    console.error("❌ Request interceptor error:", error);
    return Promise.reject(error);
  }
);

// Add response interceptor for better error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      console.error("🚫 401 Unauthorized - Token may be invalid or expired");
      console.error("Response data:", error.response.data);

      // Clear invalid token
      localStorage.removeItem("token");

      // Redirect to login if needed
      if (window.location.pathname !== '/login') {
        console.log("🔄 Redirecting to login due to 401 error");
        // You might want to redirect to login here
      }
    }
    return Promise.reject(error);
  }
);

export default api;

export const authApi = axios.create({
  baseURL: env("AUTH_SERVER_URL"),
  withCredentials: true,
});