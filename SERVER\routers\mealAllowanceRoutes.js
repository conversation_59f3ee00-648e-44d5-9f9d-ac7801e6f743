const {
    createMealAllowance,
    getAllMealAllowances,
    updateMealAllowance,
    deleteMealAllowance,
    getMealAllowanceStats
  } = require("../controllers/mealAllowanceController");
  const Router = require("express").Router;
  
// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');
  
  const mealAllowanceRouter = Router();

// 🔒 SECURED ROUTES
  
  // List all meal allowances
  mealAllowanceRouter.get("/meal-allowance", ...authenticatedRoute(), getAllMealAllowances);
  // Create a meal allowance entry
  mealAllowanceRouter.post(
    "/meal-allowance",
    ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
    createMealAllowance
  );
  // Update a meal allowance entry
  mealAllowanceRouter.put(
    "/meal-allowance/:id",
    ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
    updateMealAllowance
  );
  // Delete a meal allowance entry
  mealAllowanceRouter.delete(
    "/meal-allowance/:id",
    ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN),
    deleteMealAllowance
  );
  // Get meal allowance statistics
  mealAllowanceRouter.get(
    "/meal-allowance/stats",
    ...authenticatedRoute(),
    getMealAllowanceStats
  );
  
  module.exports = mealAllowanceRouter;
  
