import React, { useEffect, useState, useRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  TextField,
  Autocomplete,
  MenuItem,
  Typography,
  Box,
  CircularProgress,
} from "@mui/material";
import {
  EmojiEvents as LoyaltyIcon,
  Edit as EditIcon,
} from "@mui/icons-material";
import { Controller, useForm } from "react-hook-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import CustomButton from "../../global/components/CustomButton";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";
import {
  calculateLoyaltyPayYearsOfService,
  getEligibleLoyaltyPayYear,
  formatCutoffDate
} from "../../utils/loyaltyPayUtils";

const LoyaltyPayDialog = ({ row, endpoint, dataListName, schema, onDialogClose }) => {
  const isEditing = Boolean(row);
  const [open, setOpen] = useState(false);
  const [employees, setEmployees] = useState([]);
  const [settings, setSettings] = useState(null);
  const { currentUser } = useUser();
  const queryClient = useQueryClient();

  const { control, handleSubmit, reset, watch, setValue } = useForm({
    defaultValues: {
      employee: row?.employee || null,
      positionTitle: row?.positionTitle || "",
      department: row?.department || "",
      division: row?.division || "",
      region: row?.region || "",
      yearsInService: row?.yearsInService || "",
    },
  });

  const selectedEmployee = watch("employee");
  const yearsInService = watch("yearsInService");

  // Refs to manage one-time toast
  const toastShownRef = useRef(false);

  useEffect(() => {
    fetchEmployees();
    fetchSettings();
  }, []);

  useEffect(() => {
    if (selectedEmployee && !isEditing) {
      // Set form values based on selected employee
      setValue("positionTitle", selectedEmployee.positionTitle || "");
      setValue("department", selectedEmployee.department || "");
      setValue("division", selectedEmployee.division || "");
      setValue("region", selectedEmployee.region || "");
      
      // Calculate years in service based on DateOfAppointment with June 22 cutoff
      if (selectedEmployee.dateOfAppointment) {
        const fiscalYear = settings?.fiscalYear || new Date().getFullYear().toString();
        const cutoffDate = settings?.loyaltyPay?.cutoffDate || "06-22";

        // Calculate years of service with June 22 cutoff
        const yearsOfService = calculateLoyaltyPayYearsOfService(
          selectedEmployee.dateOfAppointment,
          fiscalYear,
          cutoffDate
        );

        // Get the eligible loyalty pay year (milestone)
        const eligibleYear = getEligibleLoyaltyPayYear(yearsOfService);

        console.log(`Employee: ${selectedEmployee.employeeFullName}`);
        console.log(`Appointment Date: ${selectedEmployee.dateOfAppointment}`);
        console.log(`Fiscal Year: ${fiscalYear}`);
        console.log(`Cutoff Date: ${formatCutoffDate(cutoffDate)}`);
        console.log(`Years of Service (with cutoff): ${yearsOfService}`);
        console.log(`Eligible Year: ${eligibleYear}`);

        // Set years in service to the valid milestone year
        if (eligibleYear !== null) {
          setValue("yearsInService", eligibleYear);
        }
      }
    }
  }, [selectedEmployee, setValue, isEditing]);

  const fetchEmployees = async () => {
    try {
      // Use the correct endpoint from your EmployeeList_router.js
      const res = await api.get("/employees/loyalty-pay");
      console.log("Eligible employees:", res.data);
      setEmployees(res.data);
    } catch (err) {
      toast.error("Failed to fetch eligible employees.");
      console.error("Error fetching eligible employees:", err);
    }
  };

  const fetchSettings = async () => {
    try {
      const res = await api.get("/settings/active");
      console.log("Fetched settings:", res.data);
      setSettings(res.data);
    } catch (err) {
      toast.error("Failed to fetch settings.");
    }
  };

  // computeAmount function – tumatanggap na ng parameter na 'yrs'
  const computeAmount = (yrs) => {
    if (!settings) {
      return 0;
    }
    const loyalty = settings.loyaltyPay;
    if (
      !loyalty ||
      loyalty.baseAmount === undefined ||
      loyalty.succeedingAmount === undefined
    ) {
      if (!toastShownRef.current) {
        toast.error("Loyalty Pay settings are incomplete.");
        toastShownRef.current = true;
      }
      return 0;
    }
    toastShownRef.current = false;
    const validYears = [10, 15, 20, 25, 30, 35, 40, 45, 50];
    const yrsNum = Number(yrs);
    if (!validYears.includes(yrsNum)) {
      return 0;
    }
    // Kung 10, ibalik ang baseAmount; kung hindi, ibalik ang succeedingAmount.
    return yrsNum === 10 ? loyalty.baseAmount : loyalty.succeedingAmount;
  };

  const mutation = useMutation({
    mutationFn: async (data) => {
      const processBy = `${currentUser.FirstName} ${currentUser.LastName}`;
      const processDate = new Date();
      const fiscalYear =
        settings?.fiscalYear || new Date().getFullYear().toString();
        const budgetType = settings?.budgetType || "";
      const payload = {
        employeeNumber: isEditing
          ? row.employeeNumber
          : data.employee?.employeeNumber,
        employeeFullName: isEditing
          ? row.employeeFullName
          : data.employee?.employeeFullName,
        positionTitle: data.positionTitle,
        department: data.department,
        division: data.division,
        region: data.region,
        yearsInService: Number(data.yearsInService),
        appointmentDate: isEditing
          ? row.appointmentDate
          : data.employee?.dateOfAppointment, // Include appointment date for cutoff calculation
        // Gamitin ang computeAmount function na may parameter
        amount: computeAmount(data.yearsInService),
        processBy,
        processDate,
        fiscalYear,
        budgetType,
      };

      return isEditing
        ? await api.put(`${endpoint}/${row._id}`, payload)
        : await api.post(endpoint, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries([dataListName]);
      toast.success(isEditing ? "Record updated" : "Record created");
      handleClose();
    },
    onError: (err) => {
      toast.error(err.response?.data?.error || "Something went wrong");
    },
  });

  const onSubmit = (data) => {
    mutation.mutate(data);
  };

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    reset();
    setOpen(false);
    // Optional: tawagin ang callback para isara ang menu sa parent component
    if (onDialogClose) {
      onDialogClose();
    }
  };

  return (
    <>
      {!row ? (
        <CustomButton onClick={handleOpen} size="large">
          Add Loyalty Pay
        </CustomButton>
      ) : (
        <MenuItem onClick={handleOpen} disableRipple sx={{ display: "flex", gap: 1 }}>
          <EditIcon fontSize="small" />
          Edit
        </MenuItem>
      )}

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
          }
        }}
      >
        <DialogTitle sx={{
          background: 'linear-gradient(135deg, #375e38 0%, #2e4d30 100%)',
          color: 'white',
          fontWeight: 'bold'
        }}>
          <Box display="flex" alignItems="center" gap={2}>
            <LoyaltyIcon />
            <Box>
              <Typography variant="h6" fontWeight="bold">
                {isEditing ? "Edit Loyalty Pay" : "Add Loyalty Pay"}
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                {isEditing ? "Update employee loyalty pay details" : "Add new loyalty pay record"}
              </Typography>
              {settings?.loyaltyPay?.cutoffDate && (
                <Typography variant="caption" sx={{ opacity: 0.8, display: 'block', mt: 0.5 }}>
                  Loyalty Pay Cutoff: {formatCutoffDate(settings.loyaltyPay.cutoffDate)} each year
                </Typography>
              )}
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Controller
                name="employee"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    options={employees}
                    getOptionLabel={(option) => 
                      `${option.employeeFullName} - ${option.yearsInService} years of service`
                    }
                    isOptionEqualToValue={(option, value) =>
                      option._id === value._id
                    }
                    renderOption={(props, option) => (
                      <li {...props} key={option._id}>
                        <div>
                          <strong>{option.employeeFullName}</strong>
                          <div style={{ fontSize: '0.8rem', color: 'gray' }}>
                            Position: {option.positionTitle} | Years: {option.yearsInService}
                          </div>
                        </div>
                      </li>
                    )}
                    value={
                      isEditing
                        ? employees.find(
                            (emp) =>
                              emp.employeeFullName === row.employeeFullName
                          ) || null
                        : field.value || null
                    }
                    onChange={(e, value) => !isEditing && field.onChange(value)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Eligible Employee"
                        fullWidth
                        disabled={isEditing}
                      />
                    )}
                  />
                )}
              />
            </Grid>

            <Grid item xs={6}>
              <Controller
                name="positionTitle"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Position Title"
                    fullWidth
                    disabled
                  />
                )}
              />
            </Grid>

            <Grid item xs={6}>
              <Controller
                name="yearsInService"
                control={control}
                defaultValue={row?.yearsInService || ""}
                rules={{
                  required: "Required",
                  validate: (value) =>
                    [10, 15, 20, 25, 30, 35, 40, 45, 50].includes(
                      Number(value)
                    ) || "Pumili lamang ng valid na value",
                }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    select
                    label="Years in Service"
                    {...field}
                    onChange={(e) => {
                      // Convert to number before setting the value
                      field.onChange(Number(e.target.value));
                    }}
                    fullWidth
                    error={!!error}
                    helperText={error ? error.message : ""}
                  >
                    {[10, 15, 20, 25, 30, 35, 40, 45, 50].map((year) => (
                      <MenuItem key={year} value={year}>
                        {year}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>

            <Grid item xs={6}>
              <Controller
                name="department"
                control={control}
                render={({ field }) => (
                  <TextField {...field} label="Department" fullWidth disabled />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Controller
                name="division"
                control={control}
                render={({ field }) => (
                  <TextField {...field} label="Division" fullWidth disabled />
                )}
              />
            </Grid>
            <Grid item xs={6}>
              <Controller
                name="region"
                control={control}
                render={({ field }) => (
                  <TextField {...field} label="Region" fullWidth disabled />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Computed Amount"
                value={computeAmount(yearsInService).toLocaleString("en-PH", {
                  style: "currency",
                  currency: "PHP",
                })}
                fullWidth
                disabled
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={handleClose}
            variant="outlined"
            sx={{ mr: 1 }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            disabled={mutation.isLoading}
            sx={{
              background: 'linear-gradient(135deg, #375e38 0%, #2e4d30 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #2e4d30 0%, #1e3320 100%)',
              }
            }}
          >
            {mutation.isLoading ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              isEditing ? "Update" : "Save"
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default LoyaltyPayDialog;
