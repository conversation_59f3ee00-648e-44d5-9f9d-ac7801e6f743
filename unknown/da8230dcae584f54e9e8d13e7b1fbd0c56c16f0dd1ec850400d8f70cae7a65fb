const {
  getAllSpecialCounselAllowances,
  getSpecialCounselAllowanceById,
  createSpecialCounselAllowance,
  updateSpecialCounselAllowance,
  deleteSpecialCounselAllowance,
} = require("../controllers/SpecialCounselAllowanceController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const specialCounselAllowanceRouter = Router();

// 🔒 SECURED ROUTES

specialCounselAllowanceRouter.get("/specialCounselAllowances", ...authenticatedRoute(), getAllSpecialCounselAllowances);
specialCounselAllowanceRouter.get("/specialCounselAllowances/:id", ...authenticatedRoute(), getSpecialCounselAllowanceById);
specialCounselAllowanceRouter.post("/specialCounselAllowances", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), createSpecialCounselAllowance);
specialCounselAllowanceRouter.put("/specialCounselAllowances/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), updateSpecialCounselAllowance);
specialCounselAllowanceRouter.delete("/specialCounselAllowances/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), deleteSpecialCounselAllowance);

module.exports = specialCounselAllowanceRouter;
