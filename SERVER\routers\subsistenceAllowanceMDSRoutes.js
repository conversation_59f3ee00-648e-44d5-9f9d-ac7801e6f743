const {
  createSubsistenceAllowance,
  getAllSubsistenceAllowances,
  updateSubsistenceAllowance,
  deleteSubsistenceAllowance,
  getAllPerServicesMDS,
  getAllPerServicesMDSByParams,
} = require("../controllers/subsistenceAllowanceMDSController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS
} = require('../middleware/securityMiddleware');

const subsistenceAllowanceRouter = Router();

// 🔒 SECURED ROUTES

subsistenceAllowanceRouter.get("/subsistence-allowance-mds", ...authenticatedRoute(), getAllSubsistenceAllowances);
subsistenceAllowanceRouter.post("/subsistence-allowance-mds", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), createSubsistenceAllowance);
subsistenceAllowanceRouter.put("/subsistence-allowance-mds/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), updateSubsistenceAllowance);
subsistenceAllowanceRouter.delete("/subsistence-allowance-mds/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN), deleteSubsistenceAllowance);
subsistenceAllowanceRouter.get("/getpersonnelsmds", ...authenticatedRoute(), getAllPerServicesMDS);
subsistenceAllowanceRouter.get("/getpersonnelsmds/byParams", ...authenticatedRoute(), getAllPerServicesMDSByParams);

module.exports = subsistenceAllowanceRouter;